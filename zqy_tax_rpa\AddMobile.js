const Untils = require('./untils')
const untils = new Untils()
const pbottleRPA = require('./pbottleRPA')
const key = require('./chaojiying-info.js');

let getAddMobileEWM = async ( proItem, childrenTask ) => {
    // pbottleRPA.openURL(`https://tpass.jiangsu.chinatax.gov.cn:8443/#/userCenter/phoneMaintainP`)
    pbottleRPA.openURL(`https://tpass.${proItem.url}.chinatax.gov.cn:8443/#/userCenter/phoneMaintainP`)
    let sjhmxg = await untils.waitImage('/input/1920/sjhmxg.png')
    pbottleRPA.moveMouseSmooth(sjhmxg.x,sjhmxg.y)
    pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.defaultPhone > div.phoneInfo > button')
    pbottleRPA.sleep(1000)
    pbottleRPA.browserCMD_click('body > div.el-dialog__wrapper > div > div.el-dialog__body > div.el-row > div > form > div > div:nth-child(3) > div > div > div > button > span')
    let ecewm = await untils.existImage2('/input/1920/ecewm.png')
    if(ecewm){
        console.log('开始获取人脸二维码')
        const qrCode = pbottleRPA.browserCMD_attr('div#qrcodeDiv > img','src')
        console.log(qrCode)
        const res = await untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            JSON.stringify({qrCode: qrCode}),
            1,
            `上传二维码成功`
        )
    }else{
        getAddMobileEWM(proItem,childrenTask)
    }
}

let getAddMobileEWMResult = async ( childrenTask ) => {
    for(let index = 0; index <= 300 ; index++){
        console.log('等待二维码扫描结果，剩余'+(300-index)+'秒')
        const xsjhm = await untils.existImage2('/input/1920/xsjhm.png')
        console.log('xsjhm', xsjhm)
        if(xsjhm){
            console.log('二维码扫描完成')
            // 跳过两个节点
            let res1 = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
                1,
                `二维码扫描完成`
            )
            let res2 = await untils.completeTask(
                res1.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'user_refresh',
                childrenTask.variable,
                1,
                `二维码扫描完成`
            )
            break;
        }

        if(index === 300){
            console.log('用户未扫描，二维码超时')
            let res = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 0 }),
                1,
                `二维码超时，请刷新二维码重新获取`
            )
            return
        }
    }
}

let enterMobile = async ( childrenTask ) => {
    let bsysjhm = JSON.parse(childrenTask.variable).teX
    const xsjhm = await untils.waitImage('/input/1920/xsjhm.png')
    pbottleRPA.moveMouseSmooth(xsjhm.x,xsjhm.y)
    pbottleRPA.mouseClick()
    pbottleRPA.keyTap('tab')
    pbottleRPA.keyTap('ctrl+a')
    pbottleRPA.paste(bsysjhm)
    pbottleRPA.browserCMD_click('div.el-dialog__body > div.el-row > div > form > div > div:nth-child(2) > div > div > div > div:nth-child(2) > button')
    pbottleRPA.sleep(000)
    await recognize_text()
    // 文字识别 
    async function recognize_text(){
        pbottleRPA.moveMouseSmooth(810,444)
        const res1 = pbottleRPA.aiOcr('screen',960,400,140,40)
        const text = JSON.parse(JSON.stringify(res1))[0].text
        console.log(text,text.length)
        untils.addLog(global.traceId, text, 'recognize_text')
        if(text.length != 4){
            console.log('上方图片文字识别个数有误')
            untils.addLog(global.traceId, '上方图片文字识别个数有误', 'recognize_text')
            await refreshText()
        }else{
            let image = pbottleRPA.browserCMD_attr('div[class="bg-img-div"] img', 'src')
            // console.log(image.split('base64,')[1])
            let base64 = image.split('base64,')[1]
            let res
            let keyIndex = 0;
            while (true) {
                res = await untils.recognizeText(key[keyIndex], base64);
                console.log('res', res);
                untils.addLog(global.traceId, `${JSON.stringify(res)}`, 'recognize_text');
                
                if (res.err_no == 0) {
                    break;
                }
                
                keyIndex++;
                if (keyIndex >= key.length) {
                    console.log('暂无可用的额度');
                    untils.addLog(global.traceId, '暂无可用的额度', 'recognize_text');
                    untils.terminateTask(
                        childrenTask.flowId,
                        3,
                        childrenTask.variable,
                        '验证失败，请联系管理员'
                    )
                    return; 
                }
            }
            untils.addLog(global.traceId, `${JSON.stringify(res)}`, 'recognize_text')
            const textArray = res.pic_str.split('|')
            console.log(textArray)
            if(textArray.length < 4){
                console.log('下方图片文字识别个数有误')
                untils.addLog(global.traceId, '下方图片文字识别个数有误', 'recognize_text')
                await refreshText()
            }else{
                // 创建一个map来存储字符和其在textArray中的位置
                let charIndexMap = new Map();
                textArray.forEach((item, index) => {
                    let char = item.split(',')[0]
                    charIndexMap.set(char, index)
                })
                // 存储结果的数组
                let result = [];
                // 遍历text中的每个字符
                for (let i = 0; i < text.length; i++) {
                    let char = text[i];
                    if (charIndexMap.has(char)) {
                        result.push(charIndexMap.get(char)); 
                    } else {
                        untils.addLog(global.traceId, `字符 ${char} 不在数组中`, 'recognize_text')
                        console.log(`字符 ${char} 不在数组中`);
                        result = []; 
                        break;
                    }
                }
                console.log(result); 
                if(result.length != 4){
                    await refreshText(key)
                }else{
                    console.log('识别成功')
                    untils.addLog(global.traceId, '识别成功', 'recognize_text')
                    for(let i = 0; i < 4 ; i++){
                        let char = textArray[result[i]]
                        console.log(textArray[result[i]])
                        pbottleRPA.moveMouseSmooth(795 + Number(char.split(',')[1] * 3 / 5 ), 440 + Number(char.split(',')[2] * 6 / 11))
                        pbottleRPA.mouseClick()
                        pbottleRPA.sleep(500)
                    }
                    pbottleRPA.moveMouseSmooth(950,650)
                    pbottleRPA.mouseClick()
                    pbottleRPA.sleep(2000)
                    const refreshIcon = await untils.existImage2('/input/1920/refresh.png')
                    if(refreshIcon){
                        untils.addLog(global.traceId, `未验证成功，重新验证`, 'recognize_text')
                        await refreshText()
                    }else{
                        untils.completeTask(
                            childrenTask.id,
                            childrenTask.flowId,
                            childrenTask.flowKey,
                            childrenTask.taskKey,
                            childrenTask.variable,
                            1,
                            `验证成功`
                        )
                    }
                }
            }
        }
        }
    
        async function refreshText(){
            const refreshIcon = await untils.waitImage('/input/1920/refresh.png')
            pbottleRPA.moveMouseSmooth(refreshIcon.x, refreshIcon.y)
            pbottleRPA.mouseClick()
            pbottleRPA.sleep(2000)
            await recognize_text()
        } 
	
    // // 旋转滑块验证
    // let slider_back = ''
	// let slider_block = ''
    // pbottleRPA.browserCMD_click('div.el-dialog__body > div.el-row > div > form > div > div:nth-child(2) > div > div > div > div:nth-child(2) > button')
    // const slider = await untils.waitImage('/input/1920/slider.png')
    // pbottleRPA.moveMouseSmooth(slider.x, slider.y)
    // pbottleRPA.moveMouseSmooth(slider.x + 225, slider.y + 40)
    // const img = await refresh()
    // const res = await untils.getRotateDistance(img.target,img.back)
    // console.log(`res: ${JSON.stringify(res.data.rotation_angle)}`)
    // const distance  = res.data.rotation_angle
    // console.log(`四舍五入之后的distance: `,Math.round(distance * 242 / 360))
    // pbottleRPA.moveMouseSmooth(slider.x, slider.y)
    // pbottleRPA.mouseLeftDragTo(slider.x + Math.round(distance * 242 / 360) , slider.y)
    // pbottleRPA.sleep(2000)
    // untils.completeTask(
    //     childrenTask.id,
    //     childrenTask.flowId,
    //     childrenTask.flowKey,
    //     childrenTask.taskKey,
    //     childrenTask.variable,
    //     1,
    //     `获取短信验证码成功`
    // )
     
    // async function refresh(){
    //     pbottleRPA.sleep(1000)
    //     let target = await pbottleRPA.browserCMD_attr('div#tpass-captcha-slider-img-div img', 'src')
    //     let back = await pbottleRPA.browserCMD_attr('div[class="bg-img-div"] img', 'src')
    //     if(target == 'ok' || back == 'ok'){
    //         pbottleRPA.mouseClick()
    //         return await refresh();
    //     }else{
    //         return {target,back}
    //     }
    // } 


    // 旧滑块验证
    // for( let index = 0; index < 10 ; index++ ){
    //     pbottleRPA.browserCMD_click('div.el-dialog__body > div.el-row > div > form > div > div:nth-child(2) > div > div > div > div:nth-child(2) > button')
    //     pbottleRPA.sleep(1000)
    //     let varifyImage = await pbottleRPA.browserCMD_attr('div#slideVerify img', 'src')
    //     if(varifyImage != 'ok'){
	// 		slider_back = JSON.parse(varifyImage)[0] 
	// 		slider_block = JSON.parse(varifyImage)[1] 
	// 		// console.log('imagecode[0]',slider_back)
	// 		// console.log('imagecode[1]',slider_block)
	// 	}else{
	// 		slider_back = 'data:image/jpg;base64,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'
	// 		slider_block = 'data:image/png;base64,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'
	// 	}
    //     const getCapDetialRes  = untils.getCapDetial(slider_block,slider_back)
    //     console.log('得到的结果',getCapDetialRes.captcha_solution2.target[0])
    //     let distance = getCapDetialRes.captcha_solution2.target[0]
    //     let slider  = await untils.waitImage('/input/1920/slider.png')
    //     pbottleRPA.moveMouseSmooth(slider.x,slider.y)
    //     pbottleRPA.mouseLeftDragTo(slider.x + distance + 5,slider.y)
    //     pbottleRPA.sleep(1000)
    //     const errMsg = pbottleRPA.browserCMD_text('body > div.el-message > p')
    //     const getCode = await untils.existImage2('/input/1920/hqyzm.png')
    //     const sliderComfirm = await untils.existImage2('/input/1920/slider.png')
    //     console.log('getCode',getCode)
    //     console.log('sliderComfirm',sliderComfirm)
    //     untils.addLog(global.traceId, `添加手机号码，点击发送短信验证码结果errMsg:${errMsg}`, 'enter_mobile')
    //     if(errMsg.includes('成功')){
    //         console.log('成功',errMsg)
    //         let res = await untils.completeTask(
    //             childrenTask.id,
    //             childrenTask.flowId,
    //             childrenTask.flowKey,
    //             childrenTask.taskKey,
    //             childrenTask.variable,
    //             1,
    //             `获取短信验证码成功`
    //         )
    //         break;
    //     }else if(errMsg != 'ok' && errMsg != '20s超时'){
    //         console.log('有误',errMsg)
    //         let res1 = await untils.completeTask(
    //             childrenTask.id,
    //             childrenTask.flowId,
    //             childrenTask.flowKey,
    //             childrenTask.taskKey,
    //             childrenTask.variable,
    //             1,
    //             `获取短信验证码成功`
    //         )
    //         pbottleRPA.sleep(1000)
    //         untils.terminateTask(
    //             childrenTask.flowId,
    //             3,
    //             childrenTask.variable,
    //             errMsg
    //         )
    //         break;
    //     }
    //     if(sliderComfirm){
    //         // 只有这一种情况要刷新滑块
    //         pbottleRPA.moveMouseSmooth(slider.x-500,slider.y)
    //         pbottleRPA.mouseClick()
    //     }
    // }  
    
}

let getAddMobileResult = async (childrenTask) => {
    pbottleRPA.sleep(2000)
    // let VeriCode = '961691'
    let VeriCode = JSON.parse(childrenTask.variable).smsCode
    console.log("输入短信验证码")
    pbottleRPA.browserCMD_click(`input[placeholder="请输入短信验证码"] `)
    pbottleRPA.keyTap('ctrl+a')
    pbottleRPA.paste(VeriCode)
    pbottleRPA.browserCMD_click(`body > div:nth-child(12) > div > div.el-dialog__footer > span > button.el-button.el-button--primary`)
    let errMsg = 'ok'
    for(let i = 0; i < 3; i++){
        errMsg = pbottleRPA.browserCMD_text('body > div.el-message > p')
        console.log(errMsg)
    }
    untils.addLog(global.traceId, `添加手机号码结果:${errMsg}`, 'get_add_mobile_result')
    if(errMsg.includes('重复添加') || errMsg.includes('成功')){
        console.log("手机号已存在")
        let res = await untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            childrenTask.variable,
            1,
            errMsg
        )
    }else{
        untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            errMsg
        )
    }
}

module.exports = {
    getAddMobileEWM,
    getAddMobileEWMResult,
    enterMobile,
    getAddMobileResult,
};