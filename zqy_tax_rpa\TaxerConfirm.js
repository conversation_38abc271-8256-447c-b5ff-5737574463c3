const Untils = require('./untils')
const untils = new Untils()
const pbottleRPA = require('./pbottleRPA')
const { readyToRPAToLogin } = require('./bridge2.js');

let taxerConfirmLogin = async ( proItem, childrenTask ) => {
    await readyToRPAToLogin(proItem, pbottleRPA, childrenTask)
    pbottleRPA.sleep(1000)
    console.log('切换自然人')
    untils.addLog(global.traceId, `切换自然人`, 'taxerConfirm_login')
    pbottleRPA.browserCMD_click('#app > div > div.loginCls > div.mainCls > div > div.login_box > div.password_ddd > div.tabsCls > div:nth-child(1) > div:nth-child(2) > span')
    let ready =await untils.waitImage("/input/1920/zrrLogin.png")
    pbottleRPA.moveMouseSmooth(ready.x, ready.y)
    pbottleRPA.mouseClick()
    pbottleRPA.keyTap('ctrl+a')
    pbottleRPA.paste(JSON.parse(childrenTask.variable).account)
    pbottleRPA.keyTap('tab')
    pbottleRPA.keyTap('ctrl+a')
    pbottleRPA.paste(JSON.parse(childrenTask.variable).password)
    console.log('完成账密输入')
    // 文字识别
    pbottleRPA.sleep(500)
    pbottleRPA.browserCMD_click(`button span:contains(登录)`)
    // 完成账密输入
    untils.completeTask(
        childrenTask.id,
        childrenTask.flowId,
        childrenTask.flowKey,
        childrenTask.taskKey,
        childrenTask.variable,
        1,
        `账密输入成功`
    )
}

let commitNaturePerson = async ( childrenTask ) => {
    let xm = JSON.parse(childrenTask.variable).name
    pbottleRPA.sleep(2000)
    await pbottleRPA.browserCMD_click('body > div:nth-child(3) > div > div.page-content > div:nth-child(1) > div > div.leftMain > div.leftTop.showShadow > div:nth-child(1) > div.title > img')
    let zzrxm = await pbottleRPA.browserCMD_text(`body > div:nth-child(3) > div > div.page-content > div:nth-child(1) > div > div.leftMain > div.leftTop.showShadow > div:nth-child(1) > div.title > span`)
    let iszrr = await pbottleRPA.browserCMD_text(`body > div:nth-child(3) > div > div.page-content > div:nth-child(1) > div > div.leftMain > div.leftTop.showShadow > div:nth-child(1) > div.Atext.clearfix.jbtextA > span`)
    console.log('zzrxm', zzrxm)
    console.log('iszrr', iszrr)
    if(iszrr === '自然人' && xm == zzrxm){
        console.log('完成节点')
        // 完成节点
        untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            childrenTask.variable,
            1,
            `自然人登录成功`
        )
    }else if(xm !== zzrxm){
        console.log('登录自然人姓名不匹配，终止节点')
        // 终止节点
        untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            '登录自然人姓名不匹配'
        )
        untils.addLog(global.traceId, `登录自然人姓名不匹配`, 'commit_taxerInfo')
    }else{
        console.log('非自然人登录，终止节点')
        untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            '非自然人登录'
        )
        untils.addLog(global.traceId, `非自然人登录，终止节点`, 'commit_taxerInfo')
    }
}

let confirmTaxer = async ( childrenTask ) => {
    pbottleRPA.keyTap('F5')
    pbottleRPA.sleep(1000)
    const back = await untils.existImage2('/input/1920/back.png')
    if(back){
        const backBtn = await untils.waitImage('/input/1920/back.png')
        pbottleRPA.moveMouseSmooth(backBtn.x, backBtn.y)
        pbottleRPA.mouseClick()
        pbottleRPA.sleep(2000)
    }
    const touxiang = await untils.waitImage('/input/1920/touxiang.png')
    pbottleRPA.moveMouseSmooth(touxiang.x,touxiang.y)
    const zhzx = await untils.waitImage('/input/1920/zhzx.png')
    pbottleRPA.moveMouseSmooth(zhzx.x,zhzx.y)
    pbottleRPA.mouseClick()
    pbottleRPA.sleep(1000)
    pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.sidebar-container.el-col.el-col-5 > div.has-logo > ul > div:nth-child(2) > li > div')
    pbottleRPA.sleep(1000)
    pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.sidebar-container.el-col.el-col-5 > div.has-logo > ul > div:nth-child(2) > li > ul > div:nth-child(2) > a > li')
    let dqrsq = await untils.existImage('/input/1920/dqrsq.png')
    console.log('dqrsq', dqrsq)
    if(dqrsq === true){
        pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.tabs-query.right_tabs_qiehuan > div > label:nth-child(2)')
        pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.footer > div.page-bar.el-row.is-justify-space-between.el-row--flex > div > div > span.el-pagination__sizes > div > div.el-input.el-input--mini.el-input--suffix > input')
        pbottleRPA.browserCMD_click('body > div.el-select-dropdown.el-popper > div.el-scrollbar > div.el-select-dropdown__wrap.el-scrollbar__wrap > ul > li:nth-child(4)')
        pbottleRPA.sleep(2000)
        let total = await pbottleRPA.browserCMD_text('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.footer > div.page-bar.el-row.is-justify-space-between.el-row--flex > div > div > span.el-pagination__total')
        console.log('开始确认税务局人员,' + total)
        untils.addLog(global.traceId, `开始确认税务局人员,${total}`, 'confirm_taxer')
        pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.footer > div.el-table.tableContent.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition > div.el-table__header-wrapper > table > thead > tr > th.el-table_1_column_1.el-table-column--selection.is-leaf.el-table__cell > div > label > span > span')
        pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.footer > div:nth-child(1) > div > button')
        // 如果页面此时有报错要直接完成，没有要确认的办税员
        let errMsg = pbottleRPA.browserCMD_text('body > div.el-message.el-message--warning > p')
        console.log('errMsg', errMsg)
        let plqr =await untils.existImage2('/input/1920/plqr.png')
        console.log('plqr', plqr)
        if(errMsg !== 'ok' && errMsg !== '20s超时'){
            console.log('无待确认企业，直接确认成功',errMsg)
            let res1 = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                childrenTask.variable,
                1,
                `无待确认企业，直接确认成功`
            )
            let res2 = await untils.completeTask(
                res1.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'get_confirm_taxer_result',
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
                1,
                `无待确认企业，直接确认成功`
            )
            let res3 = await untils.completeTask(
                res2.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'user_refresh',
                childrenTask.variable,
                1,
                `无待确认企业，直接确认成功`
            )
            return
        }
        if(plqr){
            console.log('二次确认')
            pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div:nth-child(6) > div:nth-child(1) > div > div.el-dialog__footer > span > button.el-button.el-button--primary')
            pbottleRPA.sleep(1000)
            const qrCode = await pbottleRPA.browserCMD_attr('div#authorise_qrcode1 > img','src')
            console.log('qrCode',qrCode)
            if(qrCode == 'ok'){
                console.log('无需二次确认，直接确认成功')
                let res1 = await untils.completeTask(
                    childrenTask.id,
                    childrenTask.flowId,
                    childrenTask.flowKey,
                    childrenTask.taskKey,
                    childrenTask.variable,
                    1,
                    `无需二次确认，直接确认成功`
                )
                let res2 = await untils.completeTask(
                    res1.data,
                    childrenTask.flowId,
                    childrenTask.flowKey,
                    'get_confirm_taxer_result',
                    JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
                    1,
                    `无需二次确认，直接确认成功`
                )
                let res3 = await untils.completeTask(
                    res2.data,
                    childrenTask.flowId,
                    childrenTask.flowKey,
                    'user_refresh',
                    childrenTask.variable,
                    1,
                    `无需二次确认，直接确认成功`
                )
                return
            }else{
                console.log('完成获取二维码接口')
                const res = await untils.completeTask(
                    childrenTask.id,
                    childrenTask.flowId,
                    childrenTask.flowKey,
                    childrenTask.taskKey,
                    JSON.stringify({qrCode: qrCode}),
                    1,
                    `上传二维码成功`
                )
                return
            }
        }
    }else{
        confirmTaxer(childrenTask)
    }
}

let getConfirmTaxerResult = async ( childrenTask ) => {
    for(let index = 0;index <= 300 ; index++){
        const qrwc = pbottleRPA.browserCMD_text('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div:nth-child(6) > div:nth-child(2) > div > div.el-dialog__body > span')
        console.log('qrwc', qrwc)
        // 如果没有，则表示扫码完成，跳出循环
        if(qrwc !== 'ok' && qrwc !== '20s超时'){
            pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div:nth-child(6) > div:nth-child(2) > div > div.el-dialog__footer > span > button')
            console.log('确认成功')
            let total = await pbottleRPA.browserCMD_text('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.footer > div.page-bar.el-row.is-justify-space-between.el-row--flex > div > div > span.el-pagination__total')
            console.log('剩余' + total)
            untils.addLog(global.traceId, `确认成功,剩余${total}`, 'get_confirm_taxer_result')
            let res1 = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
                1,
                qrwc
            )
            let res2 = await untils.completeTask(
                res1.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'user_refresh',
                childrenTask.variable,
                1,
                qrwc
            )
            break;
        }
        if(index === 300){
            // 完成任务，等待前端刷新,只需要完成当前get_add_taxer_result
            console.log('二维码确认超时')
            let res = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 0 }),
                1,
                `二维码超时，请刷新二维码重新获取`
            )
            return
        }
    }
}

module.exports = {
    taxerConfirmLogin,
    commitNaturePerson,
    confirmTaxer,
    getConfirmTaxerResult
};